<?php
/**
 * Test script for ticket-related endpoints
 * Tests the user_services and get_departments endpoints
 */

require_once 'config.php';

echo "=== Testing Ticket-Related Endpoints ===\n\n";

// Test user authentication first
echo "1. Testing authentication...\n";

// You'll need to replace this with a valid token from your system
$test_token = "your_test_token_here"; // Replace with actual token

// Test get_departments endpoint
echo "\n2. Testing get_departments endpoint...\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/api.php?f=get_departments');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['token' => $test_token]));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: $response\n\n";

if ($httpCode === 200) {
    $data = json_decode($response, true);
    if (is_array($data)) {
        echo "✓ Departments fetched successfully!\n";
        echo "Found " . count($data) . " departments:\n";
        foreach ($data as $dept) {
            echo "  - ID: {$dept['id']}, Name: {$dept['department_name']}\n";
        }
    } else {
        echo "✗ Invalid response format\n";
    }
} else {
    echo "✗ Failed to fetch departments\n";
}

// Test user_services endpoint
echo "\n3. Testing user_services endpoint...\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/api.php?f=user_services');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['token' => $test_token]));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: $response\n\n";

if ($httpCode === 200) {
    $data = json_decode($response, true);
    if (is_array($data)) {
        echo "✓ Services fetched successfully!\n";
        echo "Found " . count($data) . " services:\n";
        foreach ($data as $service) {
            echo "  - ID: {$service['id']}, Label: {$service['label']}, Type: {$service['type']}\n";
        }
    } else {
        echo "✗ Invalid response format\n";
    }
} else {
    echo "✗ Failed to fetch services\n";
}

// Test direct database query for departments
echo "\n4. Testing direct database query for departments...\n";

try {
    $stmt = $pdo->prepare("SELECT id, department_name FROM departments ORDER BY id ASC");
    $stmt->execute();
    
    $departments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "✓ Direct database query successful!\n";
    echo "Found " . count($departments) . " departments in database:\n";
    foreach ($departments as $dept) {
        echo "  - ID: {$dept['id']}, Name: {$dept['department_name']}\n";
    }
} catch (Exception $e) {
    echo "✗ Database query failed: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
?>
