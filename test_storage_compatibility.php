<?php
// Test storage compatibility system
require_once 'mysql.php';
require_once 'api.php';

echo "=== Testing Storage Compatibility System ===\n\n";

// Test parameters for 240GB SSD (should also match 2x500GB SSD)
$cpu_id = 1; // Dual Intel Xeon E5-2630v3
$storage_id = 1; // 240GB SSD
$bandwidth_id = 1; // 1 Gbps
$country_id = 1; // Romania
$city_id = 1; // Bucharest

echo "Test parameters:\n";
echo "- CPU ID: $cpu_id\n";
echo "- Storage ID: $storage_id (240GB SSD)\n";
echo "- Bandwidth ID: $bandwidth_id\n";
echo "- Country ID: $country_id\n";
echo "- City ID: $city_id\n\n";

// Test the compatibility function
echo "=== Testing getCompatibleStorageIds function ===\n";
$compatible_ids = getCompatibleStorageIds($storage_id);
echo "Compatible storage IDs for storage_id $storage_id: " . implode(', ', $compatible_ids) . "\n\n";

// Show what each compatible storage ID represents
foreach ($compatible_ids as $comp_id) {
    $storage_query = $pdo->prepare("SELECT name, price FROM dedicated_storages WHERE id = ?");
    $storage_query->execute([$comp_id]);
    $storage_result = $storage_query->fetch(PDO::FETCH_ASSOC);
    if ($storage_result) {
        echo "Storage ID $comp_id: {$storage_result['name']} (€{$storage_result['price']})\n";
    }
}

echo "\n=== Testing calculateServerAvailability function ===\n";
$result = calculateServerAvailability($pdo, $cpu_id, $storage_id, $bandwidth_id, $country_id, $city_id);

echo "Result:\n";
echo "- Available count: {$result['available_count']}\n";
echo "- Delivery time: {$result['delivery_time']}\n";
echo "- Stock color: {$result['stock_color']}\n";
echo "- Stock message: {$result['stock_message']}\n";
echo "- Has stock: " . ($result['has_stock'] ? 'true' : 'false') . "\n\n";

if ($result['stock_color'] === 'green' && $result['delivery_time'] === '15 minutes') {
    echo "✅ SUCCESS: Storage compatibility is working!\n";
    echo "The system found servers with 2x500GB SSD that can fulfill the 240GB SSD requirement.\n";
} elseif ($result['stock_color'] === 'orange' && $result['delivery_time'] === '1 week') {
    echo "⚠️  PARTIAL: Found CPU matches but no compatible storage configurations.\n";
} else {
    echo "❌ ISSUE: No matches found or unexpected result.\n";
}

echo "\n=== Manual verification ===\n";
echo "Let's manually check for servers with 2x500GB SSD configuration...\n";

// Check for servers with 2x500GB SSD (storage type ID 2 in storage table)
$manual_check = $pdo->prepare("
    SELECT id, label, bay1, bay2, bay3, bay4, bay5, bay6, cpu, country_id, status
    FROM inventory_dedicated_servers
    WHERE status = 'Available'
    AND country_id = ?
    AND order_id IS NULL
    AND cpu = ?
    AND (bay1 = 2 OR bay2 = 2 OR bay3 = 2 OR bay4 = 2 OR bay5 = 2 OR bay6 = 2)
    LIMIT 5
");
$manual_check->execute([$country_id, $cpu_id]);

echo "Servers with storage type 2 (500GB SSD) in their bays:\n";
while ($server = $manual_check->fetch(PDO::FETCH_ASSOC)) {
    echo "Server {$server['id']} ({$server['label']}): ";
    $bays = [];
    for ($i = 1; $i <= 6; $i++) {
        if ($server["bay$i"]) {
            $bays[] = "bay$i={$server["bay$i"]}";
        }
    }
    echo implode(', ', $bays) . "\n";
}

// Also check blade servers
echo "\nBlade servers with storage type 2:\n";
$blade_check = $pdo->prepare("
    SELECT bsi.id, bsi.label, bsi.bay1, bsi.bay2, bsi.bay3, bsi.bay4, bsi.bay5, bsi.bay6, bsi.cpu, ich.city_id, bsi.status
    FROM blade_server_inventory bsi
    LEFT JOIN inventory_chassis ich ON bsi.chassis_id = ich.id
    WHERE bsi.status = 'Available'
    AND ich.city_id = ?
    AND bsi.order_id IS NULL
    AND bsi.cpu = ?
    AND (bsi.bay1 = 2 OR bsi.bay2 = 2 OR bsi.bay3 = 2 OR bsi.bay4 = 2 OR bsi.bay5 = 2 OR bsi.bay6 = 2)
    LIMIT 5
");
$blade_check->execute([$city_id, $cpu_id]);

while ($server = $blade_check->fetch(PDO::FETCH_ASSOC)) {
    echo "Blade {$server['id']} ({$server['label']}): ";
    $bays = [];
    for ($i = 1; $i <= 6; $i++) {
        if ($server["bay$i"]) {
            $bays[] = "bay$i={$server["bay$i"]}";
        }
    }
    echo implode(', ', $bays) . "\n";
}
?>
